import Header from '@/components/navigation/Header'
import HeroSection from '@/components/home/<USER>'
import Footer from '@/components/layout/Footer'

export default function Home() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main>
        <HeroSection />

        {/* Featured Properties Section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                Featured <span className="text-blue-600">Properties</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Discover our handpicked selection of premium properties in prime locations
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[1, 2, 3].map(property => (
                <div
                  key={property}
                  className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow hover-lift"
                >
                  <div className="h-64 bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center">
                    <span className="text-white font-semibold">Property {property}</span>
                  </div>
                  <div className="p-6">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-xl font-semibold text-gray-900">Luxury Condo Unit</h3>
                      <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded">
                        Featured
                      </span>
                    </div>
                    <p className="text-gray-600 mb-3">Makati City, Metro Manila</p>
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-2xl font-bold text-blue-600">₱8,500,000</span>
                      <div className="text-sm text-gray-500">
                        <span>3 bed • 2 bath • 120 sqm</span>
                      </div>
                    </div>
                    <button className="w-full btn-secondary">View Details</button>
                  </div>
                </div>
              ))}
            </div>

            <div className="text-center mt-12">
              <button className="btn-primary">View All Properties</button>
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                Why Choose <span className="text-blue-600">MRH Platform</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                We provide unmatched expertise and service in the Philippine real estate market
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                {
                  icon: '🏆',
                  title: 'Award Winning',
                  description: 'Recognized excellence in real estate services'
                },
                {
                  icon: '🔍',
                  title: 'Expert Analysis',
                  description: 'Deep market insights and professional guidance'
                },
                {
                  icon: '🤝',
                  title: 'Trusted Service',
                  description: 'Transparent and reliable property transactions'
                },
                {
                  icon: '📱',
                  title: 'Modern Platform',
                  description: 'Cutting-edge technology for seamless experience'
                }
              ].map((feature, index) => (
                <div key={index} className="text-center p-6 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-blue-600 to-blue-800">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-4xl font-bold text-white mb-6">Ready to Find Your Dream Property?</h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Join thousands of satisfied clients who found their perfect property with MRH Platform
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                Browse Properties
              </button>
              <button className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                List Your Property
              </button>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  )
}
